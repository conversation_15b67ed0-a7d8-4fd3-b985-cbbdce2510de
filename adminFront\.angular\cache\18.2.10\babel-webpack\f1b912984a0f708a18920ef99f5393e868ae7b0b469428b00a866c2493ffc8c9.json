{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./request-item-import.service\";\nimport * as i2 from \"@angular/common\";\nfunction RequestItemImportButtonComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon);\n  }\n}\nexport class RequestItemImportButtonComponent {\n  constructor(importService) {\n    this.importService = importService;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.text = '需求項目匯入';\n    this.icon = 'fas fa-download';\n    this.buttonClass = 'btn btn-info mr-2';\n    this.disabled = false;\n    this.itemsImported = new EventEmitter();\n    this.beforeOpen = new EventEmitter();\n    this.error = new EventEmitter();\n  }\n  getButtonClass() {\n    return this.buttonClass || 'btn btn-info mr-2';\n  }\n  openImportDialog() {\n    this.beforeOpen.emit();\n    const serviceConfig = {\n      buildCaseId: this.buildCaseId,\n      houseType: this.houseType,\n      buttonText: this.text,\n      buttonIcon: this.icon,\n      buttonClass: this.buttonClass,\n      ...this.config\n    };\n    this.importService.openImportDialog(serviceConfig).subscribe({\n      next: result => {\n        if (result) {\n          this.itemsImported.emit(result);\n        }\n      },\n      error: error => {\n        this.error.emit('開啟匯入對話框時發生錯誤');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RequestItemImportButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportButtonComponent)(i0.ɵɵdirectiveInject(i1.RequestItemImportService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportButtonComponent,\n      selectors: [[\"app-request-item-import-button\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        houseType: \"houseType\",\n        text: \"text\",\n        icon: \"icon\",\n        buttonClass: \"buttonClass\",\n        disabled: \"disabled\",\n        config: \"config\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\",\n        beforeOpen: \"beforeOpen\",\n        error: \"error\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 5,\n      consts: [[3, \"click\", \"disabled\"], [\"style\", \"margin-right: 0.25rem;\", 3, \"class\", 4, \"ngIf\"], [2, \"margin-right\", \"0.25rem\"]],\n      template: function RequestItemImportButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function RequestItemImportButtonComponent_Template_button_click_0_listener() {\n            return ctx.openImportDialog();\n          });\n          i0.ɵɵtemplate(1, RequestItemImportButtonComponent_i_1_Template, 1, 2, \"i\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.getButtonClass());\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || !ctx.buildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelement", "ɵɵclassMap", "ctx_r0", "icon", "RequestItemImportButtonComponent", "constructor", "importService", "buildCaseId", "houseType", "text", "buttonClass", "disabled", "itemsImported", "beforeOpen", "error", "getButtonClass", "openImportDialog", "emit", "serviceConfig", "buttonText", "buttonIcon", "config", "subscribe", "next", "result", "ɵɵdirectiveInject", "i1", "RequestItemImportService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportButtonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "RequestItemImportButtonComponent_Template_button_click_0_listener", "ɵɵtemplate", "RequestItemImportButtonComponent_i_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "i2", "NgIf", "encapsulation"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import-button.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RequestItemImportService, RequestItemImportServiceConfig } from './request-item-import.service';\r\nimport { RequestItemImportConfig } from './request-item-import.component';\r\n\r\n@Component({\r\n  selector: 'app-request-item-import-button',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  template: `\r\n    <button\r\n      [disabled]=\"disabled || !buildCaseId\"\r\n      [class]=\"getButtonClass()\"\r\n      (click)=\"openImportDialog()\">\r\n      <i *ngIf=\"icon\" [class]=\"icon\" style=\"margin-right: 0.25rem;\"></i>\r\n      {{ text }}\r\n    </button>\r\n  `\r\n})\r\nexport class RequestItemImportButtonComponent {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Input() text: string = '需求項目匯入';\r\n  @Input() icon: string = 'fas fa-download';\r\n  @Input() buttonClass: string = 'btn btn-info mr-2';\r\n  @Input() disabled: boolean = false;\r\n  @Input() config?: RequestItemImportServiceConfig;\r\n\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n  @Output() beforeOpen = new EventEmitter<void>();\r\n  @Output() error = new EventEmitter<string>();\r\n\r\n  constructor(private importService: RequestItemImportService) { }\r\n\r\n  getButtonClass(): string {\r\n    return this.buttonClass || 'btn btn-info mr-2';\r\n  }\r\n\r\n  openImportDialog() {\r\n    this.beforeOpen.emit();\r\n\r\n    const serviceConfig: RequestItemImportServiceConfig = {\r\n      buildCaseId: this.buildCaseId,\r\n      houseType: this.houseType,\r\n      buttonText: this.text,\r\n      buttonIcon: this.icon,\r\n      buttonClass: this.buttonClass,\r\n      ...this.config\r\n    };\r\n\r\n    this.importService.openImportDialog(serviceConfig)\r\n      .subscribe({\r\n        next: (result) => {\r\n          if (result) {\r\n            this.itemsImported.emit(result);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.error.emit('開啟匯入對話框時發生錯誤');\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;;;;;;IAexCC,EAAA,CAAAC,SAAA,WAAkE;;;;IAAlDD,EAAA,CAAAE,UAAA,CAAAC,MAAA,CAAAC,IAAA,CAAc;;;AAKpC,OAAM,MAAOC,gCAAgC;EAa3CC,YAAoBC,aAAuC;IAAvC,KAAAA,aAAa,GAAbA,aAAa;IAZxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,IAAI,GAAW,QAAQ;IACvB,KAAAN,IAAI,GAAW,iBAAiB;IAChC,KAAAO,WAAW,GAAW,mBAAmB;IACzC,KAAAC,QAAQ,GAAY,KAAK;IAGxB,KAAAC,aAAa,GAAG,IAAIf,YAAY,EAA2B;IAC3D,KAAAgB,UAAU,GAAG,IAAIhB,YAAY,EAAQ;IACrC,KAAAiB,KAAK,GAAG,IAAIjB,YAAY,EAAU;EAEmB;EAE/DkB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACL,WAAW,IAAI,mBAAmB;EAChD;EAEAM,gBAAgBA,CAAA;IACd,IAAI,CAACH,UAAU,CAACI,IAAI,EAAE;IAEtB,MAAMC,aAAa,GAAmC;MACpDX,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBW,UAAU,EAAE,IAAI,CAACV,IAAI;MACrBW,UAAU,EAAE,IAAI,CAACjB,IAAI;MACrBO,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B,GAAG,IAAI,CAACW;KACT;IAED,IAAI,CAACf,aAAa,CAACU,gBAAgB,CAACE,aAAa,CAAC,CAC/CI,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACZ,aAAa,CAACK,IAAI,CAACO,MAAM,CAAC;QACjC;MACF,CAAC;MACDV,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,CAACG,IAAI,CAAC,cAAc,CAAC;MACjC;KACD,CAAC;EACN;;;uCA1CWb,gCAAgC,EAAAL,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAhCvB,gCAAgC;MAAAwB,SAAA;MAAAC,MAAA;QAAAtB,WAAA;QAAAC,SAAA;QAAAC,IAAA;QAAAN,IAAA;QAAAO,WAAA;QAAAC,QAAA;QAAAU,MAAA;MAAA;MAAAS,OAAA;QAAAlB,aAAA;QAAAC,UAAA;QAAAC,KAAA;MAAA;MAAAiB,UAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UATzCxC,EAAA,CAAA0C,cAAA,gBAG+B;UAA7B1C,EAAA,CAAA2C,UAAA,mBAAAC,kEAAA;YAAA,OAASH,GAAA,CAAAxB,gBAAA,EAAkB;UAAA,EAAC;UAC5BjB,EAAA,CAAA6C,UAAA,IAAAC,6CAAA,eAA8D;UAC9D9C,EAAA,CAAA+C,MAAA,GACF;UAAA/C,EAAA,CAAAgD,YAAA,EAAS;;;UAJPhD,EAAA,CAAAE,UAAA,CAAAuC,GAAA,CAAAzB,cAAA,GAA0B;UAD1BhB,EAAA,CAAAiD,UAAA,aAAAR,GAAA,CAAA7B,QAAA,KAAA6B,GAAA,CAAAjC,WAAA,CAAqC;UAGjCR,EAAA,CAAAkD,SAAA,EAAU;UAAVlD,EAAA,CAAAiD,UAAA,SAAAR,GAAA,CAAArC,IAAA,CAAU;UACdJ,EAAA,CAAAkD,SAAA,EACF;UADElD,EAAA,CAAAmD,kBAAA,MAAAV,GAAA,CAAA/B,IAAA,MACF;;;qBATAX,YAAY,EAAAqD,EAAA,CAAAC,IAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}