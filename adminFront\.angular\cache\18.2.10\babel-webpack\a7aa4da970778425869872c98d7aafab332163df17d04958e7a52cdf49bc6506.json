{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nlet RequestItemImportButtonComponent = class RequestItemImportButtonComponent {\n  constructor(importService) {\n    this.importService = importService;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.text = '需求項目匯入';\n    this.icon = 'fas fa-download';\n    this.buttonClass = 'btn btn-info mr-2';\n    this.disabled = false;\n    this.itemsImported = new EventEmitter();\n    this.beforeOpen = new EventEmitter();\n    this.error = new EventEmitter();\n  }\n  getButtonClass() {\n    return this.buttonClass || 'btn btn-info mr-2';\n  }\n  openImportDialog() {\n    this.beforeOpen.emit();\n    const serviceConfig = {\n      buildCaseId: this.buildCaseId,\n      houseType: this.houseType,\n      buttonText: this.text,\n      buttonIcon: this.icon,\n      buttonClass: this.buttonClass,\n      ...this.config\n    };\n    this.importService.openImportDialog(serviceConfig).subscribe({\n      next: result => {\n        if (result) {\n          this.itemsImported.emit(result);\n        }\n      },\n      error: error => {\n        this.error.emit('開啟匯入對話框時發生錯誤');\n      }\n    });\n  }\n};\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"houseType\", void 0);\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"text\", void 0);\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"icon\", void 0);\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"buttonClass\", void 0);\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], RequestItemImportButtonComponent.prototype, \"config\", void 0);\n__decorate([Output()], RequestItemImportButtonComponent.prototype, \"itemsImported\", void 0);\n__decorate([Output()], RequestItemImportButtonComponent.prototype, \"beforeOpen\", void 0);\n__decorate([Output()], RequestItemImportButtonComponent.prototype, \"error\", void 0);\nRequestItemImportButtonComponent = __decorate([Component({\n  selector: 'app-request-item-import-button',\n  standalone: true,\n  imports: [CommonModule, NbButtonModule, NbIconModule],\n  template: `\n    <button\n      [disabled]=\"disabled || !buildCaseId\"\n      [class]=\"getButtonClass()\"\n      (click)=\"openImportDialog()\">\n      <i *ngIf=\"icon\" [class]=\"icon\" class=\"me-1\"></i>\n      {{ text }}\n    </button>\n  `\n})], RequestItemImportButtonComponent);\nexport { RequestItemImportButtonComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "RequestItemImportButtonComponent", "constructor", "importService", "buildCaseId", "houseType", "text", "icon", "buttonClass", "disabled", "itemsImported", "beforeOpen", "error", "getButtonClass", "openImportDialog", "emit", "serviceConfig", "buttonText", "buttonIcon", "config", "subscribe", "next", "result", "__decorate", "selector", "standalone", "imports", "NbButtonModule", "NbIconModule", "template"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import-button.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RequestItemImportService, RequestItemImportServiceConfig } from './request-item-import.service';\r\nimport { RequestItemImportConfig } from './request-item-import.component';\r\n\r\n@Component({\r\n  selector: 'app-request-item-import-button',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    NbButtonModule,\r\n    NbIconModule\r\n  ],\r\n  template: `\r\n    <button\r\n      [disabled]=\"disabled || !buildCaseId\"\r\n      [class]=\"getButtonClass()\"\r\n      (click)=\"openImportDialog()\">\r\n      <i *ngIf=\"icon\" [class]=\"icon\" class=\"me-1\"></i>\r\n      {{ text }}\r\n    </button>\r\n  `\r\n})\r\nexport class RequestItemImportButtonComponent {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Input() text: string = '需求項目匯入';\r\n  @Input() icon: string = 'fas fa-download';\r\n  @Input() buttonClass: string = 'btn btn-info mr-2';\r\n  @Input() disabled: boolean = false;\r\n  @Input() config?: RequestItemImportServiceConfig;\r\n\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n  @Output() beforeOpen = new EventEmitter<void>();\r\n  @Output() error = new EventEmitter<string>();\r\n\r\n  constructor(private importService: RequestItemImportService) { }\r\n\r\n  getButtonClass(): string {\r\n    return this.buttonClass || 'btn btn-info mr-2';\r\n  }\r\n\r\n  openImportDialog() {\r\n    this.beforeOpen.emit();\r\n\r\n    const serviceConfig: RequestItemImportServiceConfig = {\r\n      buildCaseId: this.buildCaseId,\r\n      houseType: this.houseType,\r\n      buttonText: this.text,\r\n      buttonIcon: this.icon,\r\n      buttonClass: this.buttonClass,\r\n      ...this.config\r\n    };\r\n\r\n    this.importService.openImportDialog(serviceConfig)\r\n      .subscribe({\r\n        next: (result) => {\r\n          if (result) {\r\n            this.itemsImported.emit(result);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.error.emit('開啟匯入對話框時發生錯誤');\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAsBvC,IAAMC,gCAAgC,GAAtC,MAAMA,gCAAgC;EAa3CC,YAAoBC,aAAuC;IAAvC,KAAAA,aAAa,GAAbA,aAAa;IAZxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,IAAI,GAAW,QAAQ;IACvB,KAAAC,IAAI,GAAW,iBAAiB;IAChC,KAAAC,WAAW,GAAW,mBAAmB;IACzC,KAAAC,QAAQ,GAAY,KAAK;IAGxB,KAAAC,aAAa,GAAG,IAAIb,YAAY,EAA2B;IAC3D,KAAAc,UAAU,GAAG,IAAId,YAAY,EAAQ;IACrC,KAAAe,KAAK,GAAG,IAAIf,YAAY,EAAU;EAEmB;EAE/DgB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACL,WAAW,IAAI,mBAAmB;EAChD;EAEAM,gBAAgBA,CAAA;IACd,IAAI,CAACH,UAAU,CAACI,IAAI,EAAE;IAEtB,MAAMC,aAAa,GAAmC;MACpDZ,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBY,UAAU,EAAE,IAAI,CAACX,IAAI;MACrBY,UAAU,EAAE,IAAI,CAACX,IAAI;MACrBC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B,GAAG,IAAI,CAACW;KACT;IAED,IAAI,CAAChB,aAAa,CAACW,gBAAgB,CAACE,aAAa,CAAC,CAC/CI,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACZ,aAAa,CAACK,IAAI,CAACO,MAAM,CAAC;QACjC;MACF,CAAC;MACDV,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,CAACG,IAAI,CAAC,cAAc,CAAC;MACjC;KACD,CAAC;EACN;CACD;AA1CUQ,UAAA,EAARzB,KAAK,EAAE,C,oEAAyB;AACxByB,UAAA,EAARzB,KAAK,EAAE,C,kEAA0B;AACzByB,UAAA,EAARzB,KAAK,EAAE,C,6DAAyB;AACxByB,UAAA,EAARzB,KAAK,EAAE,C,6DAAkC;AACjCyB,UAAA,EAARzB,KAAK,EAAE,C,oEAA2C;AAC1CyB,UAAA,EAARzB,KAAK,EAAE,C,iEAA2B;AAC1ByB,UAAA,EAARzB,KAAK,EAAE,C,+DAAyC;AAEvCyB,UAAA,EAATxB,MAAM,EAAE,C,sEAA6D;AAC5DwB,UAAA,EAATxB,MAAM,EAAE,C,mEAAuC;AACtCwB,UAAA,EAATxB,MAAM,EAAE,C,8DAAoC;AAXlCE,gCAAgC,GAAAsB,UAAA,EAlB5C3B,SAAS,CAAC;EACT4B,QAAQ,EAAE,gCAAgC;EAC1CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1B,YAAY,EACZ2B,cAAc,EACdC,YAAY,CACb;EACDC,QAAQ,EAAE;;;;;;;;;CASX,CAAC,C,EACW5B,gCAAgC,CA2C5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}