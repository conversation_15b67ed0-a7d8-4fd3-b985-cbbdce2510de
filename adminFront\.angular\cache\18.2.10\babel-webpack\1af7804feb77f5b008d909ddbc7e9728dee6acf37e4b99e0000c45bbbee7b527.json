{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule, NbOptionModule, NbInputModule } from '@nebular/theme';\nimport { GetRequirement } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/requirement.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction RequestItemImportComponent_div_12_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"span\", 38);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 39);\n    i0.ɵɵtext(5, \"\\u8F09\\u5165\\u9700\\u6C42\\u9805\\u76EE\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequestItemImportComponent_div_12_div_28_div_1_div_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u5099\\u8A3B: \", requirement_r4.CRemark, \"\");\n  }\n}\nfunction RequestItemImportComponent_div_12_div_28_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"nb-checkbox\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_div_28_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const requirement_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(requirement_r4.selected, $event) || (requirement_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestItemImportComponent_div_12_div_28_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRequirementItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 45)(3, \"div\", 46)(4, \"span\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 50);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, RequestItemImportComponent_div_12_div_28_div_1_div_1_span_13_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const requirement_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", requirement_r4.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(requirement_r4.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", requirement_r4.CLocation || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u4F4D: \", requirement_r4.CUnit || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u50F9: \", i0.ɵɵpipeBind4(12, 6, requirement_r4.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", requirement_r4.CRemark);\n  }\n}\nfunction RequestItemImportComponent_div_12_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RequestItemImportComponent_div_12_div_28_div_1_div_1_Template, 14, 11, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.requirements);\n  }\n}\nfunction RequestItemImportComponent_div_12_div_28_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"nb-icon\", 54);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u53EF\\u532F\\u5165\\u7684\\u9700\\u6C42\\u9805\\u76EE\\uFF0C\\u8ACB\\u8ABF\\u6574\\u641C\\u5C0B\\u689D\\u4EF6\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_div_12_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, RequestItemImportComponent_div_12_div_28_div_1_Template, 2, 1, \"div\", 41)(2, RequestItemImportComponent_div_12_div_28_ng_template_2_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRequirements_r5 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.requirements.length > 0)(\"ngIfElse\", noRequirements_r5);\n  }\n}\nfunction RequestItemImportComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"nb-icon\", 19);\n    i0.ɵɵtext(4, \"\\u641C\\u5C0B\\u689D\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"div\", 21)(7, \"div\", 22)(8, \"label\", 23);\n    i0.ɵɵtext(9, \"\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CLocation, $event) || (ctx_r1.searchRequest.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 22)(12, \"label\", 25);\n    i0.ɵɵtext(13, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CRequirement, $event) || (ctx_r1.searchRequest.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 21)(16, \"div\", 27)(17, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_12_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(18, \"nb-icon\", 29);\n    i0.ɵɵtext(19, \" \\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_12_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelement(21, \"nb-icon\", 31);\n    i0.ɵɵtext(22, \" \\u641C\\u5C0B \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"div\", 18);\n    i0.ɵɵelement(25, \"nb-icon\", 33);\n    i0.ɵɵtext(26, \"\\u9078\\u64C7\\u9700\\u6C42\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, RequestItemImportComponent_div_12_div_27_Template, 6, 0, \"div\", 34)(28, RequestItemImportComponent_div_12_div_28_Template, 4, 2, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CRequirement);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction RequestItemImportComponent_div_13_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5099\\u8A3B: \", item_r6.CRemark, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"h6\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 64)(5, \"div\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 66);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 67);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_13_div_12_div_12_Template, 2, 1, \"div\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r7 + 1, \". \", item_r6.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", item_r6.CLocation || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u4F4D: \", item_r6.CUnit || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u50F9: \", i0.ɵɵpipeBind4(11, 6, item_r6.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r6.CRemark);\n  }\n}\nfunction RequestItemImportComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 55)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"nb-icon\", 56);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u532F\\u5165\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 57)(6, \"div\", 58);\n    i0.ɵɵtext(7, \" \\u5C07\\u532F\\u5165 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u500B\\u9700\\u6C42\\u9805\\u76EE\\u5230\\u76EE\\u524D\\u7684\\u5EFA\\u6848\\u4E2D \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 59);\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_13_div_12_Template, 13, 11, \"div\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedItems());\n  }\n}\nfunction RequestItemImportComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 70);\n    i0.ɵɵtext(2, \" \\u4E0A\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelement(2, \"nb-icon\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed());\n  }\n}\nfunction RequestItemImportComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.importRequirements());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 74);\n    i0.ɵɵtext(2, \" \\u78BA\\u8A8D\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RequestItemImportComponent {\n  constructor(requirementService, dialogRef) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.itemsImported = new EventEmitter();\n    this.currentStep = 1;\n    this.requirements = [];\n    this.loading = false;\n    // 搜尋相關屬性\n    this.searchRequest = {};\n  }\n  ngOnInit() {\n    this.initializeSearchForm();\n    this.loadRequirementsFromAPI();\n  }\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\n    this.searchRequest.CIsSimple = null;\n    this.searchRequest.CRequirement = '';\n    this.searchRequest.CLocation = '';\n    // 使用外部傳入的參數\n    this.searchRequest.CBuildCaseID = this.buildCaseId;\n    this.searchRequest.CHouseType = this.houseType;\n  }\n  // 搜尋事件\n  onSearch() {\n    this.loadRequirementsFromAPI();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    this.loadRequirementsFromAPI();\n  }\n  loadRequirementsFromAPI() {\n    if (!this.searchRequest.CBuildCaseID) {\n      return;\n    }\n    this.loading = true;\n    // 準備API請求參數\n    const getRequirementListArgs = {\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\n      CHouseType: this.searchRequest.CHouseType,\n      CLocation: this.searchRequest.CLocation || null,\n      CRequirement: this.searchRequest.CRequirement || null,\n      CStatus: this.searchRequest.CStatus,\n      CIsShow: this.searchRequest.CIsShow,\n      CIsSimple: this.searchRequest.CIsSimple,\n      PageIndex: 1,\n      PageSize: 100\n    };\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\n      body: getRequirementListArgs\n    }).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          this.requirements = [];\n        }\n      },\n      error: () => {\n        this.loading = false;\n        this.requirements = [];\n      }\n    });\n  }\n  onRequirementItemChange() {\n    // 當需求項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要匯入的需求項目',\n      2: '確認匯入詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  importRequirements() {\n    const config = {\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\n      buildCaseName: '',\n      selectedItems: this.getSelectedItems(),\n      totalItems: this.getSelectedItems().length,\n      searchCriteria: {\n        CHouseType: this.searchRequest.CHouseType || undefined,\n        CLocation: this.searchRequest.CLocation || undefined,\n        CRequirement: this.searchRequest.CRequirement || undefined\n      }\n    };\n    this.itemsImported.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    this.requirements.forEach(requirement => {\n      requirement.selected = false;\n    });\n  }\n  selectAll() {\n    const allSelected = this.requirements.every(item => item.selected);\n    this.requirements.forEach(item => {\n      item.selected = !allSelected;\n    });\n  }\n  getSelectedCount() {\n    return this.getSelectedItems().length;\n  }\n  getTotalCount() {\n    return this.requirements.length;\n  }\n  static {\n    this.ɵfac = function RequestItemImportComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportComponent)(i0.ɵɵdirectiveInject(i1.RequirementService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportComponent,\n      selectors: [[\"app-request-item-import\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        houseType: \"houseType\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 15,\n      consts: [[\"noRequirements\", \"\"], [1, \"request-item-import-dialog\"], [1, \"request-item-import-header\"], [1, \"request-item-import-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"request-item-import-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"request-item-import-footer\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"ghost\", \"\", \"class\", \"mr-2\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"search-section\", \"mb-4\"], [1, \"section-title\"], [\"icon\", \"search-outline\", 1, \"mr-2\"], [1, \"search-form\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-6\"], [\"for\", \"location\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"location\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"requirement-selection\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"requirement-list\", 4, \"ngIf\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"mt-2\"], [1, \"requirement-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"requirement-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"requirement-info\"], [1, \"requirement-info-row\", 2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"align-items\", \"center\", \"gap\", \"16px\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"item-remark\"], [1, \"no-requirements\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-requirements-details\"], [\"class\", \"requirement-detail-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-detail-section\"], [1, \"requirement-detail-header\"], [1, \"requirement-name\"], [1, \"requirement-meta\"], [1, \"requirement-location\"], [1, \"requirement-unit\"], [1, \"requirement-price\"], [\"class\", \"requirement-remark\", 4, \"ngIf\"], [1, \"requirement-remark\"], [\"icon\", \"arrow-back-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"icon\", \"arrow-forward-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\"], [\"icon\", \"download-outline\", 1, \"mr-1\"]],\n      template: function RequestItemImportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\", 2)(2, \"div\", 3);\n          i0.ɵɵtext(3, \"\\u9700\\u6C42\\u9805\\u76EE\\u532F\\u5165\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 6)(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u532F\\u5165\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, RequestItemImportComponent_div_12_Template, 29, 4, \"div\", 9)(13, RequestItemImportComponent_div_13_Template, 13, 2, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 10)(15, \"div\", 11);\n          i0.ɵɵtemplate(16, RequestItemImportComponent_button_16_Template, 3, 0, \"button\", 12);\n          i0.ɵɵelementStart(17, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_17_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(18, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, RequestItemImportComponent_button_19_Template, 3, 1, \"button\", 14)(20, RequestItemImportComponent_button_20_Template, 3, 0, \"button\", 15);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, i3.CurrencyPipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule, NbOptionModule, NbInputModule, i2.NbInputDirective],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.25em 0.4em;\\n  font-size: 75%;\\n  font-weight: 700;\\n  line-height: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  vertical-align: baseline;\\n  border-radius: 0.25rem;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #B8A676;\\n}\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #28A745;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #17A2B8;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  background-color: #5A5A5A;\\n}\\n\\n\\n\\n.request-item-import-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n  border-radius: 0.5rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid #E9ECEF;\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-radius: 0.5rem 0.5rem 0 0;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #FFFFFF;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: rgba(255, 255, 255, 0.8);\\n  transition: 0.3s ease;\\n  border-radius: 0.25rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.05);\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: #FFFFFF;\\n}\\n\\n\\n\\n.step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n  \\n\\n}\\n.step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 1rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #B8A676;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background: rgba(184, 166, 118, 0.03);\\n  padding: 1.5rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2C3E50;\\n  display: block;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]:focus, \\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  transition: 0.3s ease;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: #FFFFFF;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA;\\n  border-color: #CDCDCD;\\n  color: #5A5A5A;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #F5F5F5;\\n  border-color: #ADB5BD;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  transition: 0.3s ease;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  background: rgba(184, 166, 118, 0.05);\\n  transform: translateY(-1px);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:has(nb-checkbox input:checked) {\\n  border-color: #B8A676;\\n  background: rgba(184, 166, 118, 0.15);\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.2);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2C3E50;\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #A69660;\\n  font-weight: 500;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #17A2B8;\\n  font-style: italic;\\n  margin-top: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #5A5A5A;\\n  background: rgba(184, 166, 118, 0.03);\\n  border-radius: 0.375rem;\\n  border: 1px solid rgba(184, 166, 118, 0.3);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #B8A676;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border: 1px solid #B8A676;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  font-weight: 500;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n  font-weight: 700;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: #FFFFFF;\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n  transition: 0.3s ease;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  transform: translateY(-1px);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F8F6F0 0%, #F0EDE5 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid #E9ECEF;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2C3E50;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-location[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-unit[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-price[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #A69660;\\n  font-weight: 600;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-remark[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #17A2B8;\\n  font-style: italic;\\n  margin-top: 0.5rem;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: center !important;\\n  margin-bottom: 2rem !important;\\n  border-bottom: 1px solid #E9ECEF !important;\\n  padding-bottom: 1rem !important;\\n  background: transparent !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.5rem !important;\\n  margin: 0 0.5rem !important;\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  font-size: 0.9rem !important;\\n  transition: 0.3s ease !important;\\n  cursor: default !important;\\n  display: inline-block !important;\\n  text-align: center !important;\\n  min-width: 120px !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%) !important;\\n  color: #FFFFFF !important;\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.2) !important;\\n  border: none !important;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4EDDA 0%, #28A745 100%) !important;\\n  color: #FFFFFF !important;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3) !important;\\n  border: none !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #F8F9FA !important;\\n  color: #5A5A5A !important;\\n  border: 1px solid #CDCDCD !important;\\n}\\n\\n\\n\\n.request-item-import-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #E9ECEF;\\n  background: rgba(184, 166, 118, 0.03);\\n  border-radius: 0 0 0.5rem 0.5rem;\\n}\\n.request-item-import-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .request-item-import-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #675c39 0%, #6b5f3e 100%);\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: #FFFFFF;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n  background-color: #1A1A1A;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #404040 !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%) !important;\\n  color: #FFFFFF !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28A745 0%, #1e7e34 100%) !important;\\n  color: #FFFFFF !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D !important;\\n  color: #CCCCCC !important;\\n  border-color: #404040 !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.2);\\n  background-color: #3a3a3a;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:has(nb-checkbox input:checked), .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:has(nb-checkbox input:checked) {\\n  border-color: #B8A676;\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: #FFFFFF;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n  color: #CCCCCC;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #A69660 0%, #9B8A5A 100%);\\n  border-color: #B8A676;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%] {\\n  background-color: #2D2D2D;\\n  border-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(184, 166, 118, 0.2) 0%, rgba(184, 166, 118, 0.1) 100%);\\n  border-bottom-color: #404040;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(184, 166, 118, 0.05) 0%, rgba(184, 166, 118, 0.02) 100%);\\n  border-top-color: #404040;\\n}\\n\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "NbOptionModule", "NbInputModule", "GetRequirement", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "requirement_r4", "CRemark", "ɵɵtwoWayListener", "RequestItemImportComponent_div_12_div_28_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r1", "ɵɵnextContext", "onRequirementItemChange", "ɵɵtemplate", "RequestItemImportComponent_div_12_div_28_div_1_div_1_span_13_Template", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "CLocation", "CUnit", "ɵɵpipeBind4", "CUnitPrice", "ɵɵproperty", "RequestItemImportComponent_div_12_div_28_div_1_div_1_Template", "requirements", "ɵɵelement", "RequestItemImportComponent_div_12_div_28_div_1_Template", "RequestItemImportComponent_div_12_div_28_ng_template_2_Template", "ɵɵtemplateRefExtractor", "length", "noRequirements_r5", "RequestItemImportComponent_div_12_Template_input_ngModelChange_10_listener", "_r1", "searchRequest", "RequestItemImportComponent_div_12_Template_input_ngModelChange_14_listener", "RequestItemImportComponent_div_12_Template_button_click_17_listener", "resetSearch", "RequestItemImportComponent_div_12_Template_button_click_20_listener", "onSearch", "RequestItemImportComponent_div_12_div_27_Template", "RequestItemImportComponent_div_12_div_28_Template", "loading", "item_r6", "RequestItemImportComponent_div_13_div_12_div_12_Template", "ɵɵtextInterpolate2", "i_r7", "RequestItemImportComponent_div_13_div_12_Template", "getSelectedCount", "getSelectedItems", "RequestItemImportComponent_button_16_Template_button_click_0_listener", "_r8", "previousStep", "RequestItemImportComponent_button_19_Template_button_click_0_listener", "_r9", "nextStep", "canProceed", "RequestItemImportComponent_button_20_Template_button_click_0_listener", "_r10", "importRequirements", "RequestItemImportComponent", "constructor", "requirementService", "dialogRef", "buildCaseId", "houseType", "itemsImported", "currentStep", "ngOnInit", "initializeSearchForm", "loadRequirementsFromAPI", "CStatus", "CIsShow", "CIsSimple", "CBuildCaseID", "CHouseType", "getRequirementListArgs", "PageIndex", "PageSize", "apiRequirementGetRequestListForTemplatePost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "filter", "getProgressText", "progressTexts", "config", "buildCaseName", "selectedItems", "totalItems", "searchCriteria", "undefined", "emit", "close", "resetSelections", "for<PERSON>ach", "requirement", "selectAll", "allSelected", "every", "getTotalCount", "ɵɵdirectiveInject", "i1", "RequirementService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportComponent_Template", "rf", "ctx", "RequestItemImportComponent_Template_button_click_4_listener", "RequestItemImportComponent_div_12_Template", "RequestItemImportComponent_div_13_Template", "RequestItemImportComponent_button_16_Template", "RequestItemImportComponent_Template_button_click_17_listener", "RequestItemImportComponent_button_19_Template", "RequestItemImportComponent_button_20_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "NbInputDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbSelectModule,\r\n  NbOptionModule,\r\n  NbInputModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { RequirementService } from 'src/services/api/services/requirement.service';\r\nimport { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase } from 'src/services/api/models';\r\n\r\nexport interface ExtendedRequirementItem extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface RequestItemImportConfig {\r\n  buildCaseId: number;\r\n  buildCaseName?: string;\r\n  selectedItems: ExtendedRequirementItem[];\r\n  totalItems: number;\r\n  searchCriteria?: {\r\n    CHouseType?: number[];\r\n    CLocation?: string;\r\n    CRequirement?: string;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-request-item-import',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NbInputModule\r\n  ],\r\n  templateUrl: './request-item-import.component.html',\r\n  styleUrls: ['./request-item-import.component.scss']\r\n})\r\nexport class RequestItemImportComponent implements OnInit {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n\r\n  currentStep: number = 1;\r\n  requirements: ExtendedRequirementItem[] = [];\r\n  loading: boolean = false;\r\n\r\n  // 搜尋相關屬性\r\n  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};\r\n\r\n  constructor(\r\n    private requirementService: RequirementService,\r\n    private dialogRef: NbDialogRef<RequestItemImportComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.initializeSearchForm();\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\r\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\r\n    this.searchRequest.CIsSimple = null;\r\n    this.searchRequest.CRequirement = '';\r\n    this.searchRequest.CLocation = '';\r\n    // 使用外部傳入的參數\r\n    this.searchRequest.CBuildCaseID = this.buildCaseId;\r\n    this.searchRequest.CHouseType = this.houseType;\r\n  }\r\n\r\n\r\n\r\n  // 搜尋事件\r\n  onSearch() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  loadRequirementsFromAPI() {\r\n    if (!this.searchRequest.CBuildCaseID) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // 準備API請求參數\r\n    const getRequirementListArgs: GetListRequirementRequest = {\r\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\r\n      CHouseType: this.searchRequest.CHouseType,\r\n      CLocation: this.searchRequest.CLocation || null,\r\n      CRequirement: this.searchRequest.CRequirement || null,\r\n      CStatus: this.searchRequest.CStatus,\r\n      CIsShow: this.searchRequest.CIsShow,\r\n      CIsSimple: this.searchRequest.CIsSimple,\r\n      PageIndex: 1,\r\n      PageSize: 100\r\n    };\r\n\r\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\r\n      body: getRequirementListArgs\r\n    }).subscribe({\r\n      next: (response: GetRequirementListResponseBase) => {\r\n        this.loading = false;\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.requirements = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          this.requirements = [];\r\n        }\r\n      },\r\n      error: () => {\r\n        this.loading = false;\r\n        this.requirements = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onRequirementItemChange() {\r\n    // 當需求項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedRequirementItem[] {\r\n    return this.requirements.filter(item => item.selected);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要匯入的需求項目',\r\n      2: '確認匯入詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  importRequirements() {\r\n    const config: RequestItemImportConfig = {\r\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\r\n      buildCaseName: '',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalItems: this.getSelectedItems().length,\r\n      searchCriteria: {\r\n        CHouseType: this.searchRequest.CHouseType || undefined,\r\n        CLocation: this.searchRequest.CLocation || undefined,\r\n        CRequirement: this.searchRequest.CRequirement || undefined\r\n      }\r\n    };\r\n\r\n    this.itemsImported.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    this.requirements.forEach(requirement => {\r\n      requirement.selected = false;\r\n    });\r\n  }\r\n\r\n  selectAll() {\r\n    const allSelected = this.requirements.every(item => item.selected);\r\n    this.requirements.forEach(item => {\r\n      item.selected = !allSelected;\r\n    });\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.getSelectedItems().length;\r\n  }\r\n\r\n  getTotalCount(): number {\r\n    return this.requirements.length;\r\n  }\r\n}\r\n", "<nb-card class=\"request-item-import-dialog\">\r\n  <nb-card-header class=\"request-item-import-header\">\r\n    <div class=\"request-item-import-title\">需求項目匯入</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"request-item-import-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇項目</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認匯入</div>\r\n    </div>\r\n    <!-- 步驟1: 選擇需求項目 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <!-- 搜尋區塊 -->\r\n      <div class=\"search-section mb-4\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"search-outline\" class=\"mr-2\"></nb-icon>搜尋條件\r\n        </div>\r\n        <div class=\"search-form\">\r\n          <div class=\"row\">\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"location\" class=\"label mb-2\">區域</label>\r\n              <input type=\"text\" nbInput id=\"location\" placeholder=\"請輸入區域\" [(ngModel)]=\"searchRequest.CLocation\">\r\n            </div>\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"requirement\" class=\"label mb-2\">工程項目</label>\r\n              <input type=\"text\" nbInput id=\"requirement\" placeholder=\"請輸入工程項目\"\r\n                [(ngModel)]=\"searchRequest.CRequirement\">\r\n            </div>\r\n          </div>\r\n          <div class=\"row\">\r\n            <div class=\"col-12 text-right\">\r\n              <button class=\"btn btn-secondary me-2\" (click)=\"resetSearch()\">\r\n                <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n                重置\r\n              </button>\r\n              <button class=\"btn btn-primary\" (click)=\"onSearch()\">\r\n                <nb-icon icon=\"search-outline\"></nb-icon>\r\n                搜尋\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 需求項目選擇區域 -->\r\n      <div class=\"requirement-selection\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇需求項目\r\n        </div>\r\n\r\n        <!-- 載入中 -->\r\n        <div *ngIf=\"loading\" class=\"text-center py-4\">\r\n          <div class=\"spinner-border text-primary\" role=\"status\">\r\n            <span class=\"sr-only\">載入中...</span>\r\n          </div>\r\n          <div class=\"mt-2\">載入需求項目中...</div>\r\n        </div>\r\n\r\n        <!-- 需求項目列表 -->\r\n        <div *ngIf=\"!loading\" class=\"requirement-list\">\r\n          <div *ngIf=\"requirements.length > 0; else noRequirements\">\r\n            <div *ngFor=\"let requirement of requirements\" class=\"requirement-item\">\r\n              <nb-checkbox [(ngModel)]=\"requirement.selected\" (ngModelChange)=\"onRequirementItemChange()\">\r\n                <div class=\"requirement-info\">\r\n                  <div class=\"requirement-info-row\"\r\n                    style=\"display: flex; flex-wrap: wrap; align-items: center; gap: 16px;\">\r\n                    <span class=\"item-name\">{{ requirement.CRequirement || '未命名需求' }}</span>\r\n                    <span class=\"item-code\">位置: {{ requirement.CLocation || '未指定' }}</span>\r\n                    <span class=\"item-status\">單位: {{ requirement.CUnit || '未指定' }}</span>\r\n                    <span class=\"item-type\">單價: {{ requirement.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0'\r\n                      }}</span>\r\n                    <span *ngIf=\"requirement.CRemark\" class=\"item-remark\">備註: {{ requirement.CRemark }}</span>\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noRequirements>\r\n            <div class=\"no-requirements\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              沒有可匯入的需求項目，請調整搜尋條件或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認匯入 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認匯入詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將匯入 <strong>{{ getSelectedCount() }}</strong> 個需求項目到目前的建案中\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"selected-requirements-details\">\r\n          <div *ngFor=\"let item of getSelectedItems(); let i = index\" class=\"requirement-detail-section\">\r\n            <div class=\"requirement-detail-header\">\r\n              <h6 class=\"requirement-name\">{{ i + 1 }}. {{ item.CRequirement || '未命名需求' }}</h6>\r\n              <div class=\"requirement-meta\">\r\n                <div class=\"requirement-location\">位置: {{ item.CLocation || '未指定' }}</div>\r\n                <div class=\"requirement-unit\">單位: {{ item.CUnit || '未指定' }}</div>\r\n                <div class=\"requirement-price\">單價: {{ item.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}</div>\r\n              </div>\r\n              <div *ngIf=\"item.CRemark\" class=\"requirement-remark\">\r\n                備註: {{ item.CRemark }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"request-item-import-footer\">\r\n    <div class=\"step-buttons\">\r\n      <button *ngIf=\"currentStep > 1\" nbButton ghost (click)=\"previousStep()\" class=\"mr-2\">\r\n        <nb-icon icon=\"arrow-back-outline\" class=\"mr-1\"></nb-icon>\r\n        上一步\r\n      </button>\r\n\r\n      <button nbButton ghost (click)=\"close()\" class=\"mr-2\">\r\n        取消\r\n      </button>\r\n\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\" (click)=\"nextStep()\">\r\n        下一步\r\n        <nb-icon icon=\"arrow-forward-outline\" class=\"mr-1\"></nb-icon>\r\n      </button>\r\n\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" (click)=\"importRequirements()\">\r\n        <nb-icon icon=\"download-outline\" class=\"mr-1\"></nb-icon>\r\n        確認匯入\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,QAER,gBAAgB;AAEvB,SAAoCC,cAAc,QAAwC,yBAAyB;;;;;;;;;;;;;ICmDvGC,EAFJ,CAAAC,cAAA,cAA8C,cACW,eAC/B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;;;;;IAeMH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,kBAAA,mBAAAC,cAAA,CAAAC,OAAA,KAA6B;;;;;;IATzFP,EADF,CAAAC,cAAA,cAAuE,sBACuB;IAA/ED,EAAA,CAAAQ,gBAAA,2BAAAC,mGAAAC,MAAA;MAAA,MAAAJ,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAb,EAAA,CAAAc,kBAAA,CAAAR,cAAA,CAAAS,QAAA,EAAAL,MAAA,MAAAJ,cAAA,CAAAS,QAAA,GAAAL,MAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAAN,MAAA;IAAA,EAAkC;IAACV,EAAA,CAAAiB,UAAA,2BAAAR,mGAAA;MAAAT,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAAiBE,MAAA,CAAAE,uBAAA,EAAyB;IAAA,EAAC;IAIrFpB,EAHJ,CAAAC,cAAA,cAA8B,cAE8C,eAChD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IACpB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAqB,UAAA,KAAAC,qEAAA,mBAAsD;IAI9DtB,EAHM,CAAAG,YAAA,EAAM,EACF,EACM,EACV;;;;IAbSH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAuB,gBAAA,YAAAjB,cAAA,CAAAS,QAAA,CAAkC;IAIjBf,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAwB,iBAAA,CAAAlB,cAAA,CAAAmB,YAAA,qCAAyC;IACzCzB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,kBAAA,mBAAAC,cAAA,CAAAoB,SAAA,6BAAwC;IACtC1B,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,mBAAAC,cAAA,CAAAqB,KAAA,6BAAoC;IACtC3B,EAAA,CAAAI,SAAA,GACpB;IADoBJ,EAAA,CAAAK,kBAAA,mBAAAL,EAAA,CAAA4B,WAAA,QAAAtB,cAAA,CAAAuB,UAAA,uCACpB;IACG7B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA8B,UAAA,SAAAxB,cAAA,CAAAC,OAAA,CAAyB;;;;;IAX1CP,EAAA,CAAAC,cAAA,UAA0D;IACxDD,EAAA,CAAAqB,UAAA,IAAAU,6DAAA,oBAAuE;IAezE/B,EAAA,CAAAG,YAAA,EAAM;;;;IAfyBH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAA8B,UAAA,YAAAZ,MAAA,CAAAc,YAAA,CAAe;;;;;IAiB5ChC,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAiC,SAAA,kBAAoD;IACpDjC,EAAA,CAAAE,MAAA,yJACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAtBVH,EAAA,CAAAC,cAAA,cAA+C;IAkB7CD,EAjBA,CAAAqB,UAAA,IAAAa,uDAAA,kBAA0D,IAAAC,+DAAA,gCAAAnC,EAAA,CAAAoC,sBAAA,CAiB7B;IAM/BpC,EAAA,CAAAG,YAAA,EAAM;;;;;IAvBEH,EAAA,CAAAI,SAAA,EAA+B;IAAAJ,EAA/B,CAAA8B,UAAA,SAAAZ,MAAA,CAAAc,YAAA,CAAAK,MAAA,KAA+B,aAAAC,iBAAA,CAAmB;;;;;;IA9C1DtC,EAHJ,CAAAC,cAAA,cAAoD,cAEjB,cACJ;IACzBD,EAAA,CAAAiC,SAAA,kBAAsD;IAAAjC,EAAA,CAAAE,MAAA,gCACxD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIAH,EAHN,CAAAC,cAAA,cAAyB,cACN,cACyB,gBACG;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,iBAAmG;IAAtCD,EAAA,CAAAQ,gBAAA,2BAAA+B,2EAAA7B,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAc,kBAAA,CAAAI,MAAA,CAAAuB,aAAA,CAAAf,SAAA,EAAAhB,MAAA,MAAAQ,MAAA,CAAAuB,aAAA,CAAAf,SAAA,GAAAhB,MAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IACpGV,EADE,CAAAG,YAAA,EAAmG,EAC/F;IAEJH,EADF,CAAAC,cAAA,eAAwC,iBACM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAC2C;IAAzCD,EAAA,CAAAQ,gBAAA,2BAAAkC,2EAAAhC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAc,kBAAA,CAAAI,MAAA,CAAAuB,aAAA,CAAAhB,YAAA,EAAAf,MAAA,MAAAQ,MAAA,CAAAuB,aAAA,CAAAhB,YAAA,GAAAf,MAAA;MAAA,OAAAV,EAAA,CAAAgB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAE9CV,EAHI,CAAAG,YAAA,EAC2C,EACvC,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACgB,kBACkC;IAAxBD,EAAA,CAAAiB,UAAA,mBAAA0B,oEAAA;MAAA3C,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAA0B,WAAA,EAAa;IAAA,EAAC;IAC5D5C,EAAA,CAAAiC,SAAA,mBAA0C;IAC1CjC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqD;IAArBD,EAAA,CAAAiB,UAAA,mBAAA4B,oEAAA;MAAA7C,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAA4B,QAAA,EAAU;IAAA,EAAC;IAClD9C,EAAA,CAAAiC,SAAA,mBAAyC;IACzCjC,EAAA,CAAAE,MAAA,sBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,eAAmC,eACN;IACzBD,EAAA,CAAAiC,SAAA,mBAAsD;IAAAjC,EAAA,CAAAE,MAAA,6CACxD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAWNH,EARA,CAAAqB,UAAA,KAAA0B,iDAAA,kBAA8C,KAAAC,iDAAA,kBAQC;IA0BnDhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhEiEH,EAAA,CAAAI,SAAA,IAAqC;IAArCJ,EAAA,CAAAuB,gBAAA,YAAAL,MAAA,CAAAuB,aAAA,CAAAf,SAAA,CAAqC;IAKhG1B,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,gBAAA,YAAAL,MAAA,CAAAuB,aAAA,CAAAhB,YAAA,CAAwC;IAyB1CzB,EAAA,CAAAI,SAAA,IAAa;IAAbJ,EAAA,CAAA8B,UAAA,SAAAZ,MAAA,CAAA+B,OAAA,CAAa;IAQbjD,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAA8B,UAAA,UAAAZ,MAAA,CAAA+B,OAAA,CAAc;;;;;IAkDdjD,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,oBAAA6C,OAAA,CAAA3C,OAAA,MACF;;;;;IARAP,EAFJ,CAAAC,cAAA,cAA+F,cACtD,aACR;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE/EH,EADF,CAAAC,cAAA,cAA8B,cACM;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAkE;;IACnGF,EADmG,CAAAG,YAAA,EAAM,EACnG;IACNH,EAAA,CAAAqB,UAAA,KAAA8B,wDAAA,kBAAqD;IAIzDnD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAV2BH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAoD,kBAAA,KAAAC,IAAA,YAAAH,OAAA,CAAAzB,YAAA,yCAA+C;IAExCzB,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,kBAAA,mBAAA6C,OAAA,CAAAxB,SAAA,6BAAiC;IACrC1B,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,kBAAA,mBAAA6C,OAAA,CAAAvB,KAAA,6BAA6B;IAC5B3B,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAK,kBAAA,mBAAAL,EAAA,CAAA4B,WAAA,QAAAsB,OAAA,CAAArB,UAAA,uCAAkE;IAE7F7B,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA8B,UAAA,SAAAoB,OAAA,CAAA3C,OAAA,CAAkB;;;;;IAnB9BP,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAAiC,SAAA,kBAAgE;IAAAjC,EAAA,CAAAE,MAAA,4CAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,kFAChD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAqB,UAAA,KAAAiC,iDAAA,oBAA+F;IAerGtD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IApBcH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAwB,iBAAA,CAAAN,MAAA,CAAAqC,gBAAA,GAAwB;IAKhBvD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA8B,UAAA,YAAAZ,MAAA,CAAAsC,gBAAA,GAAuB;;;;;;IAoBjDxD,EAAA,CAAAC,cAAA,iBAAqF;IAAtCD,EAAA,CAAAiB,UAAA,mBAAAwC,sEAAA;MAAAzD,EAAA,CAAAW,aAAA,CAAA+C,GAAA;MAAA,MAAAxC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IACrE3D,EAAA,CAAAiC,SAAA,kBAA0D;IAC1DjC,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBAA0G;IAArBD,EAAA,CAAAiB,UAAA,mBAAA2C,sEAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAA3C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IACvG9D,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAiC,SAAA,kBAA6D;IAC/DjC,EAAA,CAAAG,YAAA,EAAS;;;;IAHiDH,EAAA,CAAA8B,UAAA,cAAAZ,MAAA,CAAA6C,UAAA,GAA0B;;;;;;IAKpF/D,EAAA,CAAAC,cAAA,iBAA2F;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA+C,sEAAA;MAAAhE,EAAA,CAAAW,aAAA,CAAAsD,IAAA;MAAA,MAAA/C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAgB,WAAA,CAASE,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IACxFlE,EAAA,CAAAiC,SAAA,kBAAwD;IACxDjC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADrGf,OAAM,MAAOgE,0BAA0B;EAYrCC,YACUC,kBAAsC,EACtCC,SAAkD;IADlD,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IAbV,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACvB,KAAAC,aAAa,GAAG,IAAIpF,YAAY,EAA2B;IAErE,KAAAqF,WAAW,GAAW,CAAC;IACvB,KAAA1C,YAAY,GAA8B,EAAE;IAC5C,KAAAiB,OAAO,GAAY,KAAK;IAExB;IACA,KAAAR,aAAa,GAAyF,EAAE;EAKpG;EAEJkC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;EACAD,oBAAoBA,CAAA;IAClB,IAAI,CAACnC,aAAa,CAACqC,OAAO,GAAG,CAAC,CAAC,CAAC;IAChC,IAAI,CAACrC,aAAa,CAACsC,OAAO,GAAG,IAAI,CAAC,CAAC;IACnC,IAAI,CAACtC,aAAa,CAACuC,SAAS,GAAG,IAAI;IACnC,IAAI,CAACvC,aAAa,CAAChB,YAAY,GAAG,EAAE;IACpC,IAAI,CAACgB,aAAa,CAACf,SAAS,GAAG,EAAE;IACjC;IACA,IAAI,CAACe,aAAa,CAACwC,YAAY,GAAG,IAAI,CAACV,WAAW;IAClD,IAAI,CAAC9B,aAAa,CAACyC,UAAU,GAAG,IAAI,CAACV,SAAS;EAChD;EAIA;EACA1B,QAAQA,CAAA;IACN,IAAI,CAAC+B,uBAAuB,EAAE;EAChC;EAEA;EACAjC,WAAWA,CAAA;IACT,IAAI,CAACgC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACpC,aAAa,CAACwC,YAAY,EAAE;MACpC;IACF;IAEA,IAAI,CAAChC,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMkC,sBAAsB,GAA8B;MACxDF,YAAY,EAAE,IAAI,CAACxC,aAAa,CAACwC,YAAY;MAC7CC,UAAU,EAAE,IAAI,CAACzC,aAAa,CAACyC,UAAU;MACzCxD,SAAS,EAAE,IAAI,CAACe,aAAa,CAACf,SAAS,IAAI,IAAI;MAC/CD,YAAY,EAAE,IAAI,CAACgB,aAAa,CAAChB,YAAY,IAAI,IAAI;MACrDqD,OAAO,EAAE,IAAI,CAACrC,aAAa,CAACqC,OAAO;MACnCC,OAAO,EAAE,IAAI,CAACtC,aAAa,CAACsC,OAAO;MACnCC,SAAS,EAAE,IAAI,CAACvC,aAAa,CAACuC,SAAS;MACvCI,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAAChB,kBAAkB,CAACiB,gDAAgD,CAAC;MACvEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAwC,IAAI;QACjD,IAAI,CAACzC,OAAO,GAAG,KAAK;QACpB,IAAIyC,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAAC5D,YAAY,GAAG0D,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAChD,GAAGA,IAAI;YACP/E,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACiB,YAAY,GAAG,EAAE;QACxB;MACF,CAAC;MACD+D,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACpB,IAAI,CAACjB,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ;EAEAZ,uBAAuBA,CAAA;IACrB;EAAA;EAGFoC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACxB,YAAY,CAACgE,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC/E,QAAQ,CAAC;EACxD;EAEAgD,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACW,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAClB,gBAAgB,EAAE,CAACnB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAyB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAf,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAuB,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAACxB,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAR,kBAAkBA,CAAA;IAChB,MAAMiC,MAAM,GAA4B;MACtC5B,WAAW,EAAE,IAAI,CAAC9B,aAAa,CAACwC,YAAY,IAAI,CAAC;MACjDmB,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,IAAI,CAAC7C,gBAAgB,EAAE;MACtC8C,UAAU,EAAE,IAAI,CAAC9C,gBAAgB,EAAE,CAACnB,MAAM;MAC1CkE,cAAc,EAAE;QACdrB,UAAU,EAAE,IAAI,CAACzC,aAAa,CAACyC,UAAU,IAAIsB,SAAS;QACtD9E,SAAS,EAAE,IAAI,CAACe,aAAa,CAACf,SAAS,IAAI8E,SAAS;QACpD/E,YAAY,EAAE,IAAI,CAACgB,aAAa,CAAChB,YAAY,IAAI+E;;KAEpD;IAED,IAAI,CAAC/B,aAAa,CAACgC,IAAI,CAACN,MAAM,CAAC;IAC/B,IAAI,CAACO,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACrC,SAAS,CAACoC,KAAK,EAAE;EACxB;EAEQC,eAAeA,CAAA;IACrB,IAAI,CAACjC,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC1C,YAAY,CAAC4E,OAAO,CAACC,WAAW,IAAG;MACtCA,WAAW,CAAC9F,QAAQ,GAAG,KAAK;IAC9B,CAAC,CAAC;EACJ;EAEA+F,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAAC/E,YAAY,CAACgF,KAAK,CAAClB,IAAI,IAAIA,IAAI,CAAC/E,QAAQ,CAAC;IAClE,IAAI,CAACiB,YAAY,CAAC4E,OAAO,CAACd,IAAI,IAAG;MAC/BA,IAAI,CAAC/E,QAAQ,GAAG,CAACgG,WAAW;IAC9B,CAAC,CAAC;EACJ;EAEAxD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACC,gBAAgB,EAAE,CAACnB,MAAM;EACvC;EAEA4E,aAAaA,CAAA;IACX,OAAO,IAAI,CAACjF,YAAY,CAACK,MAAM;EACjC;;;uCAzKW8B,0BAA0B,EAAAnE,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA1BnD,0BAA0B;MAAAoD,SAAA;MAAAC,MAAA;QAAAjD,WAAA;QAAAC,SAAA;MAAA;MAAAiD,OAAA;QAAAhD,aAAA;MAAA;MAAAiD,UAAA;MAAAC,QAAA,GAAA3H,EAAA,CAAA4H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/CnClI,EAFJ,CAAAC,cAAA,iBAA4C,wBACS,aACV;UAAAD,EAAA,CAAAE,MAAA,2CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAiB,UAAA,mBAAAmH,4DAAA;YAAA,OAASD,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC;UACxD1G,EAAA,CAAAiC,SAAA,iBAAwC;UAE5CjC,EADE,CAAAG,YAAA,EAAS,EACM;UAKbH,EAHJ,CAAAC,cAAA,sBAA+C,aAEvB,aAKjB;UAAAD,EAAA,CAAAE,MAAA,kCAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChBH,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAE,MAAA,mCAAO;UACZF,EADY,CAAAG,YAAA,EAAM,EACZ;UA+ENH,EA7EA,CAAAqB,UAAA,KAAAgH,0CAAA,kBAAoD,KAAAC,0CAAA,kBA6EA;UA6BtDtI,EAAA,CAAAG,YAAA,EAAe;UAGbH,EADF,CAAAC,cAAA,0BAAmD,eACvB;UACxBD,EAAA,CAAAqB,UAAA,KAAAkH,6CAAA,qBAAqF;UAKrFvI,EAAA,CAAAC,cAAA,kBAAsD;UAA/BD,EAAA,CAAAiB,UAAA,mBAAAuH,6DAAA;YAAA,OAASL,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC;UACtC1G,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOTH,EALA,CAAAqB,UAAA,KAAAoH,6CAAA,qBAA0G,KAAAC,6CAAA,qBAKf;UAMjG1I,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;UA9ImBH,EAAA,CAAAI,SAAA,GAIrB;UAJqBJ,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAA2I,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAAzD,WAAA,QAAAyD,GAAA,CAAAzD,WAAA,MAAAyD,GAAA,CAAAzD,WAAA,MAIrB;UACqB1E,EAAA,CAAAI,SAAA,GAIrB;UAJqBJ,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAA2I,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAAzD,WAAA,QAAAyD,GAAA,CAAAzD,WAAA,MAAAyD,GAAA,CAAAzD,WAAA,MAIrB;UAGE1E,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA8B,UAAA,SAAAqG,GAAA,CAAAzD,WAAA,OAAuB;UA6EvB1E,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAA8B,UAAA,SAAAqG,GAAA,CAAAzD,WAAA,OAAuB;UAiClB1E,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAA8B,UAAA,SAAAqG,GAAA,CAAAzD,WAAA,KAAqB;UASrB1E,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAA8B,UAAA,SAAAqG,GAAA,CAAAzD,WAAA,KAAqB;UAKrB1E,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAA8B,UAAA,SAAAqG,GAAA,CAAAzD,WAAA,OAAuB;;;qBD/GlCpF,YAAY,EAAAuJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,YAAA,EACZ1J,WAAW,EAAA2J,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX7J,YAAY,EAAA6H,EAAA,CAAAiC,eAAA,EAAAjC,EAAA,CAAAkC,mBAAA,EAAAlC,EAAA,CAAAmC,qBAAA,EAAAnC,EAAA,CAAAoC,qBAAA,EACZhK,cAAc,EAAA4H,EAAA,CAAAqC,iBAAA,EACdhK,YAAY,EAAA2H,EAAA,CAAAsC,eAAA,EACZhK,gBAAgB,EAAA0H,EAAA,CAAAuC,mBAAA,EAChBhK,cAAc,EACdC,cAAc,EACdC,aAAa,EAAAuH,EAAA,CAAAwC,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}