import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RequestItemImportService, RequestItemImportServiceConfig } from './request-item-import.service';
import { RequestItemImportConfig } from './request-item-import.component';

@Component({
  selector: 'app-request-item-import-button',
  standalone: true,
  imports: [
    CommonModule
  ],
  template: `
    <button
      [disabled]="disabled || !buildCaseId"
      [class]="getButtonClass()"
      (click)="openImportDialog()">
      <i *ngIf="icon" [class]="icon" class="me-1"></i>
      {{ text }}
    </button>
  `
})
export class RequestItemImportButtonComponent {
  @Input() buildCaseId: number = 0;
  @Input() houseType: number[] = [];
  @Input() text: string = '需求項目匯入';
  @Input() icon: string = 'fas fa-download';
  @Input() buttonClass: string = 'btn btn-info mr-2';
  @Input() disabled: boolean = false;
  @Input() config?: RequestItemImportServiceConfig;

  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();
  @Output() beforeOpen = new EventEmitter<void>();
  @Output() error = new EventEmitter<string>();

  constructor(private importService: RequestItemImportService) { }

  getButtonClass(): string {
    return this.buttonClass || 'btn btn-info mr-2';
  }

  openImportDialog() {
    this.beforeOpen.emit();

    const serviceConfig: RequestItemImportServiceConfig = {
      buildCaseId: this.buildCaseId,
      houseType: this.houseType,
      buttonText: this.text,
      buttonIcon: this.icon,
      buttonClass: this.buttonClass,
      ...this.config
    };

    this.importService.openImportDialog(serviceConfig)
      .subscribe({
        next: (result) => {
          if (result) {
            this.itemsImported.emit(result);
          }
        },
        error: (error) => {
          this.error.emit('開啟匯入對話框時發生錯誤');
        }
      });
  }
}
