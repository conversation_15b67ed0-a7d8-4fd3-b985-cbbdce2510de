{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./request-item-import.service\";\nimport * as i2 from \"@angular/common\";\nfunction RequestItemImportButtonComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon + \" mr-1\");\n  }\n}\nexport class RequestItemImportButtonComponent {\n  constructor(importService) {\n    this.importService = importService;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.text = '需求項目匯入';\n    this.icon = 'fas fa-download';\n    this.buttonClass = 'btn btn-info mr-2';\n    this.disabled = false;\n    this.itemsImported = new EventEmitter();\n    this.beforeOpen = new EventEmitter();\n    this.error = new EventEmitter();\n  }\n  openImportDialog() {\n    this.beforeOpen.emit();\n    const serviceConfig = {\n      buildCaseId: this.buildCaseId,\n      houseType: this.houseType,\n      buttonText: this.text,\n      buttonIcon: this.icon,\n      buttonClass: this.buttonClass,\n      ...this.config\n    };\n    this.importService.openImportDialog(serviceConfig).subscribe({\n      next: result => {\n        if (result) {\n          this.itemsImported.emit(result);\n        }\n      },\n      error: error => {\n        this.error.emit('開啟匯入對話框時發生錯誤');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RequestItemImportButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportButtonComponent)(i0.ɵɵdirectiveInject(i1.RequestItemImportService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportButtonComponent,\n      selectors: [[\"app-request-item-import-button\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        houseType: \"houseType\",\n        text: \"text\",\n        icon: \"icon\",\n        buttonClass: \"buttonClass\",\n        disabled: \"disabled\",\n        config: \"config\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\",\n        beforeOpen: \"beforeOpen\",\n        error: \"error\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 5,\n      consts: [[\"type\", \"button\", 3, \"click\", \"disabled\"], [3, \"class\", 4, \"ngIf\"]],\n      template: function RequestItemImportButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function RequestItemImportButtonComponent_Template_button_click_0_listener() {\n            return ctx.openImportDialog();\n          });\n          i0.ɵɵtemplate(1, RequestItemImportButtonComponent_i_1_Template, 1, 2, \"i\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.buttonClass);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || !ctx.buildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf],\n      styles: [\".mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlcXVlc3QtaXRlbS1pbXBvcnQtYnV0dG9uLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLHFCQUFBO0FBQU4iLCJmaWxlIjoicmVxdWVzdC1pdGVtLWltcG9ydC1idXR0b24uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm1yLTEge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xuICAgIH1cbiAgIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvcmVxdWVzdC1pdGVtLWltcG9ydC9yZXF1ZXN0LWl0ZW0taW1wb3J0LWJ1dHRvbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxxQkFBQTtBQUFOO0FBQ0EsZ1hBQWdYIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm1yLTEge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelement", "ɵɵclassMap", "ctx_r0", "icon", "RequestItemImportButtonComponent", "constructor", "importService", "buildCaseId", "houseType", "text", "buttonClass", "disabled", "itemsImported", "beforeOpen", "error", "openImportDialog", "emit", "serviceConfig", "buttonText", "buttonIcon", "config", "subscribe", "next", "result", "ɵɵdirectiveInject", "i1", "RequestItemImportService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportButtonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "RequestItemImportButtonComponent_Template_button_click_0_listener", "ɵɵtemplate", "RequestItemImportButtonComponent_i_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "i2", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import-button.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RequestItemImportService, RequestItemImportServiceConfig } from './request-item-import.service';\r\nimport { RequestItemImportConfig } from './request-item-import.component';\r\n\r\n@Component({\r\n  selector: 'app-request-item-import-button',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  template: `\r\n    <button\r\n      type=\"button\"\r\n      [class]=\"buttonClass\"\r\n      (click)=\"openImportDialog()\"\r\n      [disabled]=\"disabled || !buildCaseId\">\r\n      <i *ngIf=\"icon\" [class]=\"icon + ' mr-1'\"></i>\r\n      {{ text }}\r\n    </button>\r\n  `,\r\n  styles: [`\r\n    .mr-1 {\r\n      margin-right: 0.25rem;\r\n    }\r\n  `]\r\n})\r\nexport class RequestItemImportButtonComponent {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Input() text: string = '需求項目匯入';\r\n  @Input() icon: string = 'fas fa-download';\r\n  @Input() buttonClass: string = 'btn btn-info mr-2';\r\n  @Input() disabled: boolean = false;\r\n  @Input() config?: RequestItemImportServiceConfig;\r\n\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n  @Output() beforeOpen = new EventEmitter<void>();\r\n  @Output() error = new EventEmitter<string>();\r\n\r\n  constructor(private importService: RequestItemImportService) { }\r\n\r\n  openImportDialog() {\r\n    this.beforeOpen.emit();\r\n\r\n    const serviceConfig: RequestItemImportServiceConfig = {\r\n      buildCaseId: this.buildCaseId,\r\n      houseType: this.houseType,\r\n      buttonText: this.text,\r\n      buttonIcon: this.icon,\r\n      buttonClass: this.buttonClass,\r\n      ...this.config\r\n    };\r\n\r\n    this.importService.openImportDialog(serviceConfig)\r\n      .subscribe({\r\n        next: (result) => {\r\n          if (result) {\r\n            this.itemsImported.emit(result);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.error.emit('開啟匯入對話框時發生錯誤');\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;;;;;;IAgBxCC,EAAA,CAAAC,SAAA,QAA6C;;;;IAA7BD,EAAA,CAAAE,UAAA,CAAAC,MAAA,CAAAC,IAAA,WAAwB;;;AAU9C,OAAM,MAAOC,gCAAgC;EAa3CC,YAAoBC,aAAuC;IAAvC,KAAAA,aAAa,GAAbA,aAAa;IAZxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,IAAI,GAAW,QAAQ;IACvB,KAAAN,IAAI,GAAW,iBAAiB;IAChC,KAAAO,WAAW,GAAW,mBAAmB;IACzC,KAAAC,QAAQ,GAAY,KAAK;IAGxB,KAAAC,aAAa,GAAG,IAAIf,YAAY,EAA2B;IAC3D,KAAAgB,UAAU,GAAG,IAAIhB,YAAY,EAAQ;IACrC,KAAAiB,KAAK,GAAG,IAAIjB,YAAY,EAAU;EAEmB;EAE/DkB,gBAAgBA,CAAA;IACd,IAAI,CAACF,UAAU,CAACG,IAAI,EAAE;IAEtB,MAAMC,aAAa,GAAmC;MACpDV,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBU,UAAU,EAAE,IAAI,CAACT,IAAI;MACrBU,UAAU,EAAE,IAAI,CAAChB,IAAI;MACrBO,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B,GAAG,IAAI,CAACU;KACT;IAED,IAAI,CAACd,aAAa,CAACS,gBAAgB,CAACE,aAAa,CAAC,CAC/CI,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACX,aAAa,CAACI,IAAI,CAACO,MAAM,CAAC;QACjC;MACF,CAAC;MACDT,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,CAACE,IAAI,CAAC,cAAc,CAAC;MACjC;KACD,CAAC;EACN;;;uCAtCWZ,gCAAgC,EAAAL,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAhCtB,gCAAgC;MAAAuB,SAAA;MAAAC,MAAA;QAAArB,WAAA;QAAAC,SAAA;QAAAC,IAAA;QAAAN,IAAA;QAAAO,WAAA;QAAAC,QAAA;QAAAS,MAAA;MAAA;MAAAS,OAAA;QAAAjB,aAAA;QAAAC,UAAA;QAAAC,KAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAAhC,EAAA,CAAAiC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAfzCvC,EAAA,CAAAyC,cAAA,gBAIwC;UADtCzC,EAAA,CAAA0C,UAAA,mBAAAC,kEAAA;YAAA,OAASH,GAAA,CAAAxB,gBAAA,EAAkB;UAAA,EAAC;UAE5BhB,EAAA,CAAA4C,UAAA,IAAAC,6CAAA,eAAyC;UACzC7C,EAAA,CAAA8C,MAAA,GACF;UAAA9C,EAAA,CAAA+C,YAAA,EAAS;;;UALP/C,EAAA,CAAAE,UAAA,CAAAsC,GAAA,CAAA7B,WAAA,CAAqB;UAErBX,EAAA,CAAAgD,UAAA,aAAAR,GAAA,CAAA5B,QAAA,KAAA4B,GAAA,CAAAhC,WAAA,CAAqC;UACjCR,EAAA,CAAAiD,SAAA,EAAU;UAAVjD,EAAA,CAAAgD,UAAA,SAAAR,GAAA,CAAApC,IAAA,CAAU;UACdJ,EAAA,CAAAiD,SAAA,EACF;UADEjD,EAAA,CAAAkD,kBAAA,MAAAV,GAAA,CAAA9B,IAAA,MACF;;;qBAVAX,YAAY,EAAAoD,EAAA,CAAAC,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}